<?php

namespace Database\Seeders;

use App\Models\Advertisement;
use App\Models\User;
use Illuminate\Database\Seeder;

class AdvertisementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('role', 'admin')->first();

        if (!$admin) {
            $this->command->warn('No admin user found. Skipping advertisement seeding.');
            return;
        }

        $advertisements = [
            [
                'title' => 'Welcome to Sonali Microfinance',
                'description' => 'Empowering communities through accessible financial services. Join thousands of satisfied members.',
                'image' => null, // Will use placeholder
                'link_url' => null,
                'placement' => 'login_page',
                'display_order' => 1,
                'active' => true,
                'start_date' => now(),
                'end_date' => now()->addMonths(6),
                'target_audience' => 'New visitors',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Quick Loan Approval',
                'description' => 'Get your loan approved in just 24 hours. Competitive rates and flexible terms.',
                'image' => null,
                'link_url' => 'https://example.com/loan-application',
                'placement' => 'login_page',
                'display_order' => 2,
                'active' => true,
                'start_date' => now(),
                'end_date' => now()->addMonths(3),
                'target_audience' => 'Loan applicants',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Savings Account Benefits',
                'description' => 'Earn competitive interest rates on your savings. Start building your financial future today.',
                'image' => null,
                'link_url' => null,
                'placement' => 'dashboard',
                'display_order' => 1,
                'active' => true,
                'start_date' => now(),
                'target_audience' => 'All members',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Mobile Banking App',
                'description' => 'Download our mobile app for convenient banking on the go. Available on iOS and Android.',
                'image' => null,
                'link_url' => 'https://example.com/mobile-app',
                'placement' => 'sidebar',
                'display_order' => 1,
                'active' => true,
                'start_date' => now(),
                'end_date' => now()->addYear(),
                'target_audience' => 'Mobile users',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Financial Literacy Workshop',
                'description' => 'Join our free financial literacy workshops. Learn budgeting, saving, and investment basics.',
                'image' => null,
                'link_url' => null,
                'placement' => 'login_page',
                'display_order' => 3,
                'active' => false, // Inactive for testing
                'start_date' => now()->addWeek(),
                'end_date' => now()->addMonth(),
                'target_audience' => 'All members',
                'created_by' => $admin->id,
            ],
        ];

        foreach ($advertisements as $adData) {
            Advertisement::create($adData);
        }

        $this->command->info('Sample advertisements created successfully!');
    }
}
