<?php

namespace Tests\Feature;

use App\Models\Advertisement;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;
use Tests\TestCase;

class AdvertisementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_admin_can_access_advertisement_manager()
    {
        $admin = User::factory()->create(['role' => 'admin', 'is_active' => true]);

        $response = $this->actingAs($admin)
            ->get('/admin/advertisement-manager');

        $response->assertSuccessful();
        $response->assertSee('Advertisement Management');
    }

    public function test_non_admin_cannot_access_advertisement_manager()
    {
        $user = User::factory()->create(['role' => 'member', 'is_active' => true]);

        $response = $this->actingAs($user)
            ->get('/admin/advertisement-manager');

        // Should be redirected or forbidden
        $this->assertTrue(in_array($response->status(), [302, 403]));
    }

    public function test_advertisement_model_has_correct_attributes()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $ad = Advertisement::create([
            'title' => 'Test Advertisement',
            'description' => 'Test description',
            'placement' => 'login_page',
            'active' => true,
            'display_order' => 1,
            'created_by' => $admin->id,
        ]);

        $this->assertEquals('Test Advertisement', $ad->title);
        $this->assertEquals('login_page', $ad->placement);
        $this->assertTrue($ad->active);
        $this->assertEquals('Login Page', $ad->placement_label);
    }

    public function test_advertisement_scopes_work_correctly()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create active advertisement
        Advertisement::create([
            'title' => 'Active Ad',
            'placement' => 'login_page',
            'active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'created_by' => $admin->id,
        ]);

        // Create inactive advertisement
        Advertisement::create([
            'title' => 'Inactive Ad',
            'placement' => 'login_page',
            'active' => false,
            'created_by' => $admin->id,
        ]);

        // Create scheduled advertisement (future)
        Advertisement::create([
            'title' => 'Future Ad',
            'placement' => 'login_page',
            'active' => true,
            'start_date' => now()->addDay(),
            'created_by' => $admin->id,
        ]);

        $activeAds = Advertisement::active()->get();
        $scheduledAds = Advertisement::active()->scheduled()->get();

        $this->assertCount(2, $activeAds); // Active Ad and Future Ad
        $this->assertCount(1, $scheduledAds); // Only Active Ad (Future Ad not yet scheduled)
    }

    public function test_advertisement_click_tracking()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $ad = Advertisement::create([
            'title' => 'Test Ad',
            'placement' => 'login_page',
            'active' => true,
            'created_by' => $admin->id,
        ]);

        $this->assertEquals(0, $ad->click_count);
        $this->assertEquals(0, $ad->impression_count);

        $ad->recordClick();
        $ad->recordImpression();

        $ad->refresh();

        $this->assertEquals(1, $ad->click_count);
        $this->assertEquals(1, $ad->impression_count);
        $this->assertEquals(100.0, $ad->click_through_rate);
    }

    public function test_advertisement_image_url_attribute()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Test with no image
        $ad = Advertisement::create([
            'title' => 'Test Ad',
            'placement' => 'login_page',
            'active' => true,
            'created_by' => $admin->id,
        ]);

        $this->assertStringContainsString('placeholder-ad.svg', $ad->image_url);

        // Test with image
        $ad->update(['image' => 'advertisements/test.jpg']);
        $this->assertStringContainsString('test.jpg', $ad->image_url);
    }

    public function test_login_page_displays_active_advertisements()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        Advertisement::create([
            'title' => 'Login Ad',
            'description' => 'Test login advertisement',
            'placement' => 'login_page',
            'active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDay(),
            'created_by' => $admin->id,
        ]);

        $response = $this->get('/login');

        $response->assertStatus(200);
        // The login page should load successfully, ads are loaded via Livewire
        $response->assertSee('Login');
    }
}
