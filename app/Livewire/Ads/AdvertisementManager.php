<?php

namespace App\Livewire\Ads;

use App\Models\Advertisement;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Intervention\Image\Facades\Image;

class AdvertisementManager extends Component
{
    use WithFileUploads, WithPagination;

    // Form properties
    public $title = '';
    public $description = '';
    public $image = null;
    public $link_url = '';
    public $active = true;
    public $display_order = 0;
    public $start_date = '';
    public $end_date = '';
    public $target_audience = '';
    public $placement = 'login_page';

    // Modal properties
    public $showModal = false;
    public $editingAd = null;

    // Filter properties
    public $filter_placement = '';
    public $filter_status = '';
    public $search = '';

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string|max:500',
        'image' => 'nullable|image|max:2048', // 2MB max
        'link_url' => 'nullable|url|max:255',
        'active' => 'boolean',
        'display_order' => 'integer|min:0',
        'start_date' => 'nullable|date',
        'end_date' => 'nullable|date|after_or_equal:start_date',
        'target_audience' => 'nullable|string|max:100',
        'placement' => 'required|string',
    ];

    public function mount()
    {
        $this->start_date = now()->format('Y-m-d');
    }

    public function render()
    {
        $advertisements = $this->getAdvertisements();
        $placements = Advertisement::getPlacements();

        return view('livewire.ads.advertisement-manager', [
            'advertisements' => $advertisements,
            'placements' => $placements,
        ]);
    }

    private function getAdvertisements()
    {
        $query = Advertisement::with('creator');

        // Apply filters
        if ($this->filter_placement) {
            $query->where('placement', $this->filter_placement);
        }

        if ($this->filter_status === 'active') {
            $query->where('active', true);
        } elseif ($this->filter_status === 'inactive') {
            $query->where('active', false);
        }

        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }

        return $query->orderBy('placement')
                    ->orderBy('display_order')
                    ->orderBy('created_at', 'desc')
                    ->paginate(10);
    }

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->editingAd = null;
        $this->resetForm();
    }

    public function editAdvertisement($adId)
    {
        $ad = Advertisement::findOrFail($adId);

        $this->editingAd = $ad;
        $this->title = $ad->title;
        $this->description = $ad->description;
        $this->link_url = $ad->link_url;
        $this->active = $ad->active;
        $this->display_order = $ad->display_order;
        $this->start_date = $ad->start_date?->format('Y-m-d') ?? '';
        $this->end_date = $ad->end_date?->format('Y-m-d') ?? '';
        $this->target_audience = $ad->target_audience;
        $this->placement = $ad->placement;

        $this->showModal = true;
    }

    public function saveAdvertisement()
    {
        $this->validate();

        try {
            $data = [
                'title' => $this->title,
                'description' => $this->description,
                'link_url' => $this->link_url,
                'active' => $this->active,
                'display_order' => $this->display_order,
                'start_date' => $this->start_date ?: null,
                'end_date' => $this->end_date ?: null,
                'target_audience' => $this->target_audience,
                'placement' => $this->placement,
                'created_by' => Auth::id(),
            ];

            // Handle image upload
            if ($this->image) {
                $data['image'] = $this->uploadAndOptimizeImage();
            }

            if ($this->editingAd) {
                // Delete old image if new one is uploaded
                if ($this->image && $this->editingAd->image) {
                    Storage::disk('public')->delete($this->editingAd->image);
                }

                $this->editingAd->update($data);
                session()->flash('success', 'Advertisement updated successfully!');
            } else {
                Advertisement::create($data);
                session()->flash('success', 'Advertisement created successfully!');
            }

            $this->closeModal();
        } catch (\Exception $e) {
            session()->flash('error', 'Error saving advertisement: ' . $e->getMessage());
        }
    }

    private function uploadAndOptimizeImage(): string
    {
        $filename = 'ad_' . time() . '_' . uniqid() . '.' . $this->image->getClientOriginalExtension();
        $path = 'advertisements/' . $filename;

        // Optimize image using Intervention Image (if available)
        try {
            $image = Image::make($this->image->getRealPath());

            // Resize if too large (max 1200px width)
            if ($image->width() > 1200) {
                $image->resize(1200, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }

            // Compress image quality
            $image->encode('jpg', 85);

            // Store optimized image
            Storage::disk('public')->put($path, $image->stream());
        } catch (\Exception $e) {
            // Fallback to regular upload if image optimization fails
            $path = $this->image->store('advertisements', 'public');
        }

        return $path;
    }

    public function deleteAdvertisement($adId)
    {
        $ad = Advertisement::findOrFail($adId);
        $ad->delete();

        session()->flash('success', 'Advertisement deleted successfully!');
    }

    public function toggleStatus($adId)
    {
        $ad = Advertisement::findOrFail($adId);
        $ad->update(['active' => !$ad->active]);

        $status = $ad->active ? 'activated' : 'deactivated';
        session()->flash('success', "Advertisement {$status} successfully!");
    }

    public function recordClick($adId)
    {
        $ad = Advertisement::findOrFail($adId);
        $ad->recordClick();

        if ($ad->link_url) {
            return redirect($ad->link_url);
        }
    }

    public function duplicateAdvertisement($adId)
    {
        $ad = Advertisement::findOrFail($adId);

        $newAd = $ad->replicate();
        $newAd->title = $ad->title . ' (Copy)';
        $newAd->active = false;
        $newAd->click_count = 0;
        $newAd->impression_count = 0;
        $newAd->created_by = Auth::id();
        $newAd->save();

        session()->flash('success', 'Advertisement duplicated successfully!');
    }

    public function moveUp($adId)
    {
        $ad = Advertisement::findOrFail($adId);
        $previousAd = Advertisement::where('placement', $ad->placement)
            ->where('display_order', '<', $ad->display_order)
            ->orderBy('display_order', 'desc')
            ->first();

        if ($previousAd) {
            $tempOrder = $ad->display_order;
            $ad->update(['display_order' => $previousAd->display_order]);
            $previousAd->update(['display_order' => $tempOrder]);
        }
    }

    public function moveDown($adId)
    {
        $ad = Advertisement::findOrFail($adId);
        $nextAd = Advertisement::where('placement', $ad->placement)
            ->where('display_order', '>', $ad->display_order)
            ->orderBy('display_order', 'asc')
            ->first();

        if ($nextAd) {
            $tempOrder = $ad->display_order;
            $ad->update(['display_order' => $nextAd->display_order]);
            $nextAd->update(['display_order' => $tempOrder]);
        }
    }

    private function resetForm()
    {
        $this->title = '';
        $this->description = '';
        $this->image = null;
        $this->link_url = '';
        $this->active = true;
        $this->display_order = 0;
        $this->start_date = now()->format('Y-m-d');
        $this->end_date = '';
        $this->target_audience = '';
        $this->placement = 'login_page';
    }

    public function clearFilters()
    {
        $this->filter_placement = '';
        $this->filter_status = '';
        $this->search = '';
        $this->resetPage();
    }

    public function getAnalytics()
    {
        $analytics = Advertisement::selectRaw('
            placement,
            COUNT(*) as total_ads,
            SUM(click_count) as total_clicks,
            SUM(impression_count) as total_impressions,
            CASE
                WHEN SUM(impression_count) > 0
                THEN (SUM(click_count) * 100.0 / SUM(impression_count))
                ELSE 0
            END as avg_ctr
        ')
        ->groupBy('placement')
        ->get();

        // If no advertisements exist, return empty collection
        if ($analytics->isEmpty()) {
            return collect();
        }

        return $analytics;
    }
}
