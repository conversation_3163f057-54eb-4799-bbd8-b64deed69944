@if($ads->count() > 0)
    <div class="relative bg-white rounded-lg shadow-lg overflow-hidden"
         x-data="{
             currentIndex: @entangle('currentAdIndex'),
             autoRotate: @entangle('autoRotate'),
             interval: @entangle('rotationInterval'),
             timer: null,
             startAutoRotate() {
                 if (this.autoRotate && {{ $ads->count() }} > 1) {
                     this.timer = setInterval(() => {
                         this.currentIndex = (this.currentIndex + 1) % {{ $ads->count() }};
                         $wire.selectAd(this.currentIndex);
                     }, this.interval);
                 }
             },
             stopAutoRotate() {
                 if (this.timer) {
                     clearInterval(this.timer);
                     this.timer = null;
                 }
             }
         }"
         x-init="startAutoRotate()"
         @mouseenter="stopAutoRotate()"
         @mouseleave="startAutoRotate()">

        <!-- Advertisement Display -->
        <div class="relative h-64 md:h-80">
            @foreach($ads as $index => $ad)
                <div class="absolute inset-0 transition-opacity duration-500 ease-in-out"
                     x-show="currentIndex === {{ $index }}"
                     x-transition:enter="transition-opacity duration-500"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="transition-opacity duration-500"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0">

                    @if($ad->image)
                        <img src="{{ $ad->image_url }}"
                             alt="{{ $ad->title }}"
                             class="w-full h-full object-cover cursor-pointer"
                             @if($ad->link_url)
                                 wire:click="clickAd({{ $ad->id }})"
                             @endif>
                    @else
                        <div class="w-full h-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center cursor-pointer"
                             @if($ad->link_url)
                                 wire:click="clickAd({{ $ad->id }})"
                             @endif>
                            <div class="text-center text-white p-8">
                                <h3 class="text-2xl font-bold mb-2">{{ $ad->title }}</h3>
                                @if($ad->description)
                                    <p class="text-lg opacity-90">{{ $ad->description }}</p>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Overlay Content -->
                    @if($ad->image && ($ad->title || $ad->description))
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                            <h3 class="text-white text-xl font-bold mb-2">{{ $ad->title }}</h3>
                            @if($ad->description)
                                <p class="text-white/90 text-sm">{{ $ad->description }}</p>
                            @endif
                        </div>
                    @endif

                    <!-- Click-through indicator -->
                    @if($ad->link_url)
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs">
                                Click to learn more
                            </span>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Navigation Controls -->
        @if($ads->count() > 1)
            <!-- Previous/Next Buttons -->
            <button wire:click="previousAd"
                    class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-2 rounded-full transition-all duration-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <button wire:click="nextAd"
                    class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-2 rounded-full transition-all duration-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Dot Indicators -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                @foreach($ads as $index => $ad)
                    <button wire:click="selectAd({{ $index }})"
                            class="w-3 h-3 rounded-full transition-all duration-200
                                   {{ $index === $currentAdIndex ? 'bg-white' : 'bg-white/50 hover:bg-white/70' }}">
                    </button>
                @endforeach
            </div>

            <!-- Auto-rotate toggle -->
            <button @click="autoRotate = !autoRotate; autoRotate ? startAutoRotate() : stopAutoRotate()"
                    class="absolute top-4 left-4 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-2 rounded-full transition-all duration-200">
                <svg x-show="autoRotate" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <svg x-show="!autoRotate" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M15 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </button>
        @endif
    </div>

    <!-- Advertisement Analytics (Hidden, for tracking) -->
    <script>
        document.addEventListener('livewire:init', () => {
            // Track ad impressions
            @foreach($ads as $ad)
                // Ad {{ $ad->id }} impression tracked
            @endforeach
        });

        // Handle redirect events
        Livewire.on('redirect', (data) => {
            window.open(data.url, '_blank');
        });
    </script>
@endif
